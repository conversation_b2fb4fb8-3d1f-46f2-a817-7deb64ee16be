# ADHD Trading Dashboard

> **🏎️ A high-performance React trading dashboard with Formula 1-inspired design**

A modern monorepo trading analysis and journaling system built with React, TypeScript, and a sleek Formula 1 racing theme. Designed for traders who need fast, reliable tools for performance analysis and trade management.

## 🚀 Quick Start

```bash
# Install dependencies

# Start development server
yarn dev

# Build for production
yarn build
```

The app will be available at [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

This is a **monorepo** with clear separation of concerns:

```
📦 adhd-trading-dashboard-lib/
├── 📁 packages/
│   ├── 📁 shared/          # Reusable components, utilities, theme
│   └── 📁 dashboard/       # Main trading dashboard application
├── 📁 docs/               # 📚 Complete documentation
├── 📁 scripts/            # Development and build tools
└── 📄 package.json        # Root workspace configuration
```

### Package Dependencies

```
shared → dashboard
```

- **shared**: Foundation components, API layer, theme system, utilities
- **dashboard**: Trading features, pages, routing, application logic

## ✨ Features

- **🏎️ Formula 1 Theme**: High-performance design inspired by F1 racing
- **📊 Trade Analysis**: Comprehensive performance tracking and analytics
- **📝 Trade Journal**: Detailed trade logging with setup classification
- **🎯 Daily Guide**: Market overview and trading plan management
- **⚡ Real-time Data**: Fast, responsive interface for live trading
- **🔧 Developer Tools**: Enhanced development experience with hot reload
- **📱 Responsive**: Works on desktop and mobile devices
- **🎨 Atomic Design**: Scalable component architecture

## 🛠️ Available Scripts

- `yarn dev` - Start development server with hot reload
- `yarn build` - Build for production
- `yarn test` - Run test suite
- `yarn lint` - Lint code
- `yarn analyze` - Analyze code architecture
- `yarn health` - Check system health
- `yarn docs:check` - Validate documentation

## 📚 Documentation

Complete documentation is available in the [`docs/`](./docs/) directory:

- **[📖 Getting Started](./docs/GETTING_STARTED.md)** - Setup and installation
- **[🏗️ Architecture](./docs/ARCHITECTURE.md)** - System design and structure

## 🏁 Technology Stack

- **Framework**: React 18 + TypeScript
- **Styling**: styled-components with F1 racing theme
- **Build**: Vite (development) + TypeScript (production)
- **Testing**: Vitest (unit) + Playwright (E2E)
- **Package Management**: Yarn workspaces
- **State Management**: React Context + custom hooks
- **Data Storage**: IndexedDB for client-side persistence

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** following our [architecture guide](./docs/ARCHITECTURE.md)
4. **Run tests**: `yarn test`
5. **Commit changes**: `git commit -m 'feat: add amazing feature'`
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

## 📄 License

MIT License - see [LICENSE](./LICENSE) file for details.

---

**Built with ❤️ for traders who demand performance and precision**
